import { 
  Search, 
  Database, 
  Mail, 
  ArrowRight, 
  CheckCircle, 
  Clock,
  Users,
  Target,
  Zap
} from 'lucide-react'

const LeadGenerationFlow = () => {
  const flowSteps = [
    {
      id: 1,
      title: 'Lead Discovery',
      description: 'Identify potential leads through various sources',
      icon: Search,
      status: 'completed',
      details: [
        'Website visitors tracking',
        'Social media monitoring',
        'Industry databases',
        'Referral programs'
      ]
    },
    {
      id: 2,
      title: 'Apollo API Enrichment',
      description: 'Enrich lead data with Apollo\'s comprehensive database',
      icon: Database,
      status: 'active',
      details: [
        'Contact information verification',
        'Company data enrichment',
        'Social profiles discovery',
        'Technology stack identification'
      ]
    },
    {
      id: 3,
      title: 'Lead Scoring',
      description: 'Score leads based on enriched data and behavior',
      icon: Target,
      status: 'pending',
      details: [
        'Demographic scoring',
        'Behavioral analysis',
        'Company fit assessment',
        'Intent signal detection'
      ]
    },
    {
      id: 4,
      title: 'Email Nurturing',
      description: 'Automated email sequences for lead nurturing',
      icon: Mail,
      status: 'pending',
      details: [
        'Personalized email sequences',
        'Behavioral triggers',
        'A/B testing campaigns',
        'Performance tracking'
      ]
    }
  ]

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-success-500" />
      case 'active':
        return <Zap className="h-5 w-5 text-primary-500" />
      case 'pending':
        return <Clock className="h-5 w-5 text-gray-400" />
      default:
        return <Clock className="h-5 w-5 text-gray-400" />
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'border-success-200 bg-success-50'
      case 'active':
        return 'border-primary-200 bg-primary-50'
      case 'pending':
        return 'border-gray-200 bg-gray-50'
      default:
        return 'border-gray-200 bg-gray-50'
    }
  }

  return (
    <div className="space-y-8">
      <div className="card">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-900">Lead Generation Flow</h2>
          <div className="flex items-center space-x-2">
            <span className="status-badge status-active">Live Processing</span>
          </div>
        </div>

        {/* Flow Visualization */}
        <div className="relative">
          <div className="flex items-center justify-between mb-8">
            {flowSteps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className={`relative p-6 rounded-lg border-2 ${getStatusColor(step.status)} min-w-0 flex-1 max-w-xs`}>
                  <div className="flex items-center justify-between mb-3">
                    <step.icon className="h-8 w-8 text-gray-700" />
                    {getStatusIcon(step.status)}
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">{step.title}</h3>
                  <p className="text-sm text-gray-600 mb-3">{step.description}</p>
                  <ul className="space-y-1">
                    {step.details.map((detail, detailIndex) => (
                      <li key={detailIndex} className="text-xs text-gray-500 flex items-center">
                        <div className="w-1 h-1 bg-gray-400 rounded-full mr-2"></div>
                        {detail}
                      </li>
                    ))}
                  </ul>
                </div>
                {index < flowSteps.length - 1 && (
                  <ArrowRight className="h-6 w-6 text-gray-400 mx-4 flex-shrink-0" />
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Apollo API Integration Details */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Apollo API Integration</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <span className="text-sm font-medium text-gray-700">API Status</span>
              <span className="status-badge status-active">Connected</span>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <span className="text-sm font-medium text-gray-700">Daily Enrichments</span>
              <span className="text-sm font-semibold text-gray-900">1,247 / 2,000</span>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <span className="text-sm font-medium text-gray-700">Success Rate</span>
              <span className="text-sm font-semibold text-success-600">94.2%</span>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <span className="text-sm font-medium text-gray-700">Avg. Response Time</span>
              <span className="text-sm font-semibold text-gray-900">1.2s</span>
            </div>
          </div>
        </div>

        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Nurturing Platform</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <span className="text-sm font-medium text-gray-700">Active Sequences</span>
              <span className="text-sm font-semibold text-gray-900">12</span>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <span className="text-sm font-medium text-gray-700">Leads in Nurturing</span>
              <span className="text-sm font-semibold text-gray-900">1,834</span>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <span className="text-sm font-medium text-gray-700">Open Rate</span>
              <span className="text-sm font-semibold text-success-600">28.4%</span>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <span className="text-sm font-medium text-gray-700">Click Rate</span>
              <span className="text-sm font-semibold text-primary-600">12.7%</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default LeadGenerationFlow
