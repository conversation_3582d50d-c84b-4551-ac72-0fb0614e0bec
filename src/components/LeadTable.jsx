import { useState } from 'react'
import { 
  Search, 
  Filter, 
  Download, 
  Mail, 
  Phone, 
  Building,
  ExternalLink,
  MoreVertical
} from 'lucide-react'

const LeadTable = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState('all')

  // Mock lead data
  const leads = [
    {
      id: 1,
      name: '<PERSON>',
      email: '<EMAIL>',
      company: 'TechCorp Solutions',
      title: 'VP of Marketing',
      phone: '+****************',
      status: 'enriched',
      score: 85,
      source: 'Website',
      lastActivity: '2 hours ago',
      apolloEnriched: true
    },
    {
      id: 2,
      name: '<PERSON>',
      email: '<EMAIL>',
      company: 'InnovateTech',
      title: 'CTO',
      phone: '+****************',
      status: 'nurturing',
      score: 92,
      source: 'LinkedIn',
      lastActivity: '1 day ago',
      apolloEnriched: true
    },
    {
      id: 3,
      name: '<PERSON>',
      email: '<EMAIL>',
      company: 'StartupXYZ',
      title: 'Founder',
      phone: '+****************',
      status: 'new',
      score: 78,
      source: 'Referral',
      lastActivity: '3 hours ago',
      apolloEnriched: false
    },
    {
      id: 4,
      name: 'David Kim',
      email: '<EMAIL>',
      company: 'Enterprise Solutions',
      title: 'Director of Sales',
      phone: '+****************',
      status: 'qualified',
      score: 88,
      source: 'Cold Outreach',
      lastActivity: '5 hours ago',
      apolloEnriched: true
    },
    {
      id: 5,
      name: 'Lisa Thompson',
      email: '<EMAIL>',
      company: 'Digital Agency Pro',
      title: 'Marketing Manager',
      phone: '+****************',
      status: 'enriched',
      score: 76,
      source: 'Website',
      lastActivity: '1 hour ago',
      apolloEnriched: true
    }
  ]

  const getStatusBadge = (status) => {
    const statusConfig = {
      new: 'status-badge bg-blue-100 text-blue-800',
      enriched: 'status-badge bg-green-100 text-green-800',
      nurturing: 'status-badge bg-yellow-100 text-yellow-800',
      qualified: 'status-badge bg-purple-100 text-purple-800',
      converted: 'status-badge bg-emerald-100 text-emerald-800'
    }
    return statusConfig[status] || 'status-badge status-inactive'
  }

  const getScoreColor = (score) => {
    if (score >= 80) return 'text-success-600 font-semibold'
    if (score >= 60) return 'text-warning-600 font-semibold'
    return 'text-gray-600'
  }

  const filteredLeads = leads.filter(lead => {
    const matchesSearch = lead.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         lead.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         lead.company.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesFilter = filterStatus === 'all' || lead.status === filterStatus
    return matchesSearch && matchesFilter
  })

  return (
    <div className="space-y-6">
      <div className="card">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-900">Lead Management</h2>
          <div className="flex items-center space-x-3">
            <button className="btn-secondary">
              <Filter className="h-4 w-4 mr-2" />
              Filter
            </button>
            <button className="btn-secondary">
              <Download className="h-4 w-4 mr-2" />
              Export
            </button>
          </div>
        </div>

        {/* Search and Filter Bar */}
        <div className="flex items-center space-x-4 mb-6">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search leads..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          >
            <option value="all">All Status</option>
            <option value="new">New</option>
            <option value="enriched">Enriched</option>
            <option value="nurturing">Nurturing</option>
            <option value="qualified">Qualified</option>
          </select>
        </div>

        {/* Leads Table */}
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Lead
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Company
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Score
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Source
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Last Activity
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredLeads.map((lead) => (
                <tr key={lead.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-full bg-primary-100 flex items-center justify-center">
                          <span className="text-sm font-medium text-primary-600">
                            {lead.name.split(' ').map(n => n[0]).join('')}
                          </span>
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="flex items-center">
                          <div className="text-sm font-medium text-gray-900">{lead.name}</div>
                          {lead.apolloEnriched && (
                            <div className="ml-2 w-2 h-2 bg-success-500 rounded-full" title="Apollo Enriched"></div>
                          )}
                        </div>
                        <div className="text-sm text-gray-500">{lead.email}</div>
                        <div className="text-sm text-gray-500">{lead.title}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <Building className="h-4 w-4 text-gray-400 mr-2" />
                      <div className="text-sm text-gray-900">{lead.company}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={getStatusBadge(lead.status)}>
                      {lead.status.charAt(0).toUpperCase() + lead.status.slice(1)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className={`text-sm ${getScoreColor(lead.score)}`}>
                      {lead.score}/100
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {lead.source}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {lead.lastActivity}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <button className="text-primary-600 hover:text-primary-900">
                        <Mail className="h-4 w-4" />
                      </button>
                      <button className="text-primary-600 hover:text-primary-900">
                        <Phone className="h-4 w-4" />
                      </button>
                      <button className="text-primary-600 hover:text-primary-900">
                        <ExternalLink className="h-4 w-4" />
                      </button>
                      <button className="text-gray-400 hover:text-gray-600">
                        <MoreVertical className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

export default LeadTable
