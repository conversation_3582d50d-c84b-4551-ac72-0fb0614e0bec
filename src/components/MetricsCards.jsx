import { Users, Mail, TrendingUp, Target } from 'lucide-react'

const MetricsCards = () => {
  const metrics = [
    {
      title: 'Total Leads',
      value: '2,847',
      change: '+12.5%',
      changeType: 'positive',
      icon: Users,
      description: 'Generated this month'
    },
    {
      title: 'Enriched Leads',
      value: '2,234',
      change: '+8.2%',
      changeType: 'positive',
      icon: Target,
      description: 'Via Apollo API'
    },
    {
      title: 'Email Campaigns',
      value: '24',
      change: '+3',
      changeType: 'positive',
      icon: Mail,
      description: 'Active campaigns'
    },
    {
      title: 'Conversion Rate',
      value: '18.4%',
      change: '+2.1%',
      changeType: 'positive',
      icon: TrendingUp,
      description: 'Lead to customer'
    }
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {metrics.map((metric, index) => {
        const Icon = metric.icon
        return (
          <div key={index} className="card">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="p-2 bg-primary-100 rounded-lg">
                  <Icon className="h-6 w-6 text-primary-600" />
                </div>
              </div>
              <span className={`text-sm font-medium ${
                metric.changeType === 'positive' ? 'text-success-600' : 'text-danger-600'
              }`}>
                {metric.change}
              </span>
            </div>
            <div className="mt-4">
              <h3 className="text-2xl font-bold text-gray-900">{metric.value}</h3>
              <p className="text-sm text-gray-600 mt-1">{metric.title}</p>
              <p className="text-xs text-gray-500 mt-1">{metric.description}</p>
            </div>
          </div>
        )
      })}
    </div>
  )
}

export default MetricsCards
