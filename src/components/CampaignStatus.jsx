import { useState } from 'react'
import { 
  Play, 
  Pause, 
  Mail, 
  Users, 
  TrendingUp, 
  Calendar,
  MoreVertical,
  Plus
} from 'lucide-react'

const CampaignStatus = () => {
  const [campaigns] = useState([
    {
      id: 1,
      name: 'Q4 Enterprise Outreach',
      status: 'active',
      type: 'Email Sequence',
      leads: 234,
      sent: 1847,
      opened: 524,
      clicked: 89,
      replied: 23,
      startDate: '2024-01-15',
      openRate: 28.4,
      clickRate: 4.8,
      replyRate: 1.2
    },
    {
      id: 2,
      name: 'SaaS Demo Follow-up',
      status: 'active',
      type: 'Nurture Sequence',
      leads: 156,
      sent: 934,
      opened: 312,
      clicked: 67,
      replied: 18,
      startDate: '2024-01-10',
      openRate: 33.4,
      clickRate: 7.2,
      replyRate: 1.9
    },
    {
      id: 3,
      name: 'Cold Outreach - Tech',
      status: 'paused',
      type: 'Cold Email',
      leads: 89,
      sent: 445,
      opened: 98,
      clicked: 12,
      replied: 3,
      startDate: '2024-01-08',
      openRate: 22.0,
      clickRate: 2.7,
      replyRate: 0.7
    },
    {
      id: 4,
      name: 'Webinar Promotion',
      status: 'scheduled',
      type: 'Event Marketing',
      leads: 567,
      sent: 0,
      opened: 0,
      clicked: 0,
      replied: 0,
      startDate: '2024-01-25',
      openRate: 0,
      clickRate: 0,
      replyRate: 0
    }
  ])

  const getStatusBadge = (status) => {
    const statusConfig = {
      active: 'status-badge status-active',
      paused: 'status-badge status-pending',
      scheduled: 'status-badge bg-blue-100 text-blue-800',
      completed: 'status-badge bg-gray-100 text-gray-800'
    }
    return statusConfig[status] || 'status-badge status-inactive'
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'active':
        return <Play className="h-4 w-4 text-success-600" />
      case 'paused':
        return <Pause className="h-4 w-4 text-warning-600" />
      case 'scheduled':
        return <Calendar className="h-4 w-4 text-blue-600" />
      default:
        return <Calendar className="h-4 w-4 text-gray-600" />
    }
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  return (
    <div className="card">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Email Campaigns</h3>
        <button className="btn-primary">
          <Plus className="h-4 w-4 mr-2" />
          New Campaign
        </button>
      </div>

      <div className="space-y-4">
        {campaigns.map((campaign) => (
          <div key={campaign.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-3">
                {getStatusIcon(campaign.status)}
                <div>
                  <h4 className="font-medium text-gray-900">{campaign.name}</h4>
                  <p className="text-sm text-gray-500">{campaign.type}</p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <span className={getStatusBadge(campaign.status)}>
                  {campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1)}
                </span>
                <button className="text-gray-400 hover:text-gray-600">
                  <MoreVertical className="h-4 w-4" />
                </button>
              </div>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-3">
              <div className="text-center">
                <div className="flex items-center justify-center mb-1">
                  <Users className="h-4 w-4 text-gray-400 mr-1" />
                </div>
                <div className="text-lg font-semibold text-gray-900">{campaign.leads}</div>
                <div className="text-xs text-gray-500">Leads</div>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center mb-1">
                  <Mail className="h-4 w-4 text-gray-400 mr-1" />
                </div>
                <div className="text-lg font-semibold text-gray-900">{campaign.sent}</div>
                <div className="text-xs text-gray-500">Sent</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold text-success-600">{campaign.openRate}%</div>
                <div className="text-xs text-gray-500">Open Rate</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold text-primary-600">{campaign.clickRate}%</div>
                <div className="text-xs text-gray-500">Click Rate</div>
              </div>
            </div>

            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center space-x-4">
                <span className="text-gray-500">
                  Started: {formatDate(campaign.startDate)}
                </span>
                <span className="text-gray-500">
                  Replies: {campaign.replied}
                </span>
              </div>
              <div className="flex items-center space-x-2">
                {campaign.status === 'active' && (
                  <button className="text-warning-600 hover:text-warning-700 text-sm font-medium">
                    Pause
                  </button>
                )}
                {campaign.status === 'paused' && (
                  <button className="text-success-600 hover:text-success-700 text-sm font-medium">
                    Resume
                  </button>
                )}
                <button className="text-primary-600 hover:text-primary-700 text-sm font-medium">
                  View Details
                </button>
              </div>
            </div>

            {/* Progress bar for active campaigns */}
            {campaign.status === 'active' && campaign.sent > 0 && (
              <div className="mt-3">
                <div className="flex justify-between text-xs text-gray-500 mb-1">
                  <span>Progress</span>
                  <span>{Math.round((campaign.sent / (campaign.leads * 3)) * 100)}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${Math.min((campaign.sent / (campaign.leads * 3)) * 100, 100)}%` }}
                  ></div>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Campaign Summary */}
      <div className="mt-6 pt-6 border-t border-gray-200">
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-gray-900">
              {campaigns.filter(c => c.status === 'active').length}
            </div>
            <div className="text-sm text-gray-500">Active Campaigns</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-gray-900">
              {campaigns.reduce((sum, c) => sum + c.leads, 0)}
            </div>
            <div className="text-sm text-gray-500">Total Leads</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-success-600">
              {(campaigns.reduce((sum, c) => sum + c.openRate, 0) / campaigns.length).toFixed(1)}%
            </div>
            <div className="text-sm text-gray-500">Avg. Open Rate</div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CampaignStatus
